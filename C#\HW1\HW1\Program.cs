using System;

namespace HW1
{
    internal class Program
    {
        static void Main(string[] args)
        {
            while (true)
            {
                Console.WriteLine("\n=== Меню ===");
                Console.WriteLine("1. Проверить, является ли число палиндромом");
                Console.WriteLine("2. Циклический сдвиг числа");
                Console.WriteLine("3. Найти самую длинную неубывающую цепочку");
                Console.WriteLine("0. Выход");
                Console.Write("Выберите задание (0-3): ");

                string choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        CheckPalindrome();
                        break;
                    case "2":
                        CyclicShift();
                        break;
                    case "3":
                        FindLongestNonDecreasingSequence();
                        break;
                    case "0":
                        Console.WriteLine("До свидания!");
                        return;
                    default:
                        Console.WriteLine("Неверный выбор. Попробуйте снова.");
                        break;
                }
            }
        }

        // Задание 1: Проверка на палиндром
        static void CheckPalindrome()
        {
            Console.Write("Введите число: ");
            string input = Console.ReadLine();

            if (string.IsNullOrEmpty(input))
            {
                Console.WriteLine("Ошибка: пустой ввод.");
                return;
            }

            // Удаляем знак минуса для проверки
            string number = input.StartsWith("-") ? input.Substring(1) : input;

            // Проверяем, что введены только цифры
            foreach (char c in number)
            {
                if (!char.IsDigit(c))
                {
                    Console.WriteLine("Ошибка: введите корректное число.");
                    return;
                }
            }

            bool isPalindrome = IsPalindrome(number);
            Console.WriteLine($"Число {input} {(isPalindrome ? "является" : "не является")} палиндромом.");
        }

        static bool IsPalindrome(string number)
        {
            int left = 0;
            int right = number.Length - 1;

            while (left < right)
            {
                if (number[left] != number[right])
                    return false;
                left++;
                right--;
            }
            return true;
        }

        // Задание 2: Циклический сдвиг
        static void CyclicShift()
        {
            Console.Write("Введите число: ");
            string input = Console.ReadLine();

            if (string.IsNullOrEmpty(input))
            {
                Console.WriteLine("Ошибка: пустой ввод.");
                return;
            }

            // Проверяем знак
            bool isNegative = input.StartsWith("-");
            string number = isNegative ? input.Substring(1) : input;

            // Проверяем, что введены только цифры
            foreach (char c in number)
            {
                if (!char.IsDigit(c))
                {
                    Console.WriteLine("Ошибка: введите корректное число.");
                    return;
                }
            }

            Console.Write("Введите количество разрядов для сдвига: ");
            if (!int.TryParse(Console.ReadLine(), out int shiftCount))
            {
                Console.WriteLine("Ошибка: введите корректное число разрядов.");
                return;
            }

            Console.Write("Введите направление сдвига (L - влево, R - вправо): ");
            string direction = Console.ReadLine()?.ToUpper();

            if (direction != "L" && direction != "R")
            {
                Console.WriteLine("Ошибка: введите L для сдвига влево или R для сдвига вправо.");
                return;
            }

            string result = PerformCyclicShift(number, shiftCount, direction == "L");
            string finalResult = isNegative ? "-" + result : result;
            Console.WriteLine($"Результат: {finalResult}");
        }

        static string PerformCyclicShift(string number, int shiftCount, bool leftShift)
        {
            if (number.Length <= 1)
                return number;

            // Нормализуем количество сдвигов
            shiftCount = shiftCount % number.Length;
            if (shiftCount == 0)
                return number;

            if (leftShift)
            {
                // Сдвиг влево: берем символы с позиции shiftCount до конца + символы с начала до shiftCount
                return number.Substring(shiftCount) + number.Substring(0, shiftCount);
            }
            else
            {
                // Сдвиг вправо: берем последние shiftCount символов + остальные символы
                int rightShiftPos = number.Length - shiftCount;
                return number.Substring(rightShiftPos) + number.Substring(0, rightShiftPos);
            }
        }

        // Задание 3: Поиск самой длинной неубывающей цепочки
        static void FindLongestNonDecreasingSequence()
        {
            Console.WriteLine("Введите 15 целых чисел:");

            int[] numbers = new int[15];
            for (int i = 0; i < 15; i++)
            {
                Console.Write($"Число {i + 1}: ");
                if (!int.TryParse(Console.ReadLine(), out numbers[i]))
                {
                    Console.WriteLine("Ошибка: введите корректное целое число.");
                    i--; // Повторить ввод для этой позиции
                }
            }

            int maxLength = 1;
            int maxStartIndex = 0;
            int currentLength = 1;
            int currentStartIndex = 0;

            for (int i = 1; i < 15; i++)
            {
                if (numbers[i] >= numbers[i - 1])
                {
                    currentLength++;
                }
                else
                {
                    if (currentLength > maxLength)
                    {
                        maxLength = currentLength;
                        maxStartIndex = currentStartIndex;
                    }
                    currentLength = 1;
                    currentStartIndex = i;
                }
            }

            // Проверяем последнюю цепочку
            if (currentLength > maxLength)
            {
                maxLength = currentLength;
                maxStartIndex = currentStartIndex;
            }

            Console.WriteLine($"Максимальная длина неубывающей цепочки: {maxLength}");
            Console.WriteLine($"Порядковый номер числа, с которого началась цепочка: {maxStartIndex + 1}");

            // Показываем саму цепочку для наглядности
            Console.Write("Цепочка: ");
            for (int i = maxStartIndex; i < maxStartIndex + maxLength; i++)
            {
                Console.Write(numbers[i]);
                if (i < maxStartIndex + maxLength - 1)
                    Console.Write(" -> ");
            }
            Console.WriteLine();
        }
    }
}