using System;

namespace HW1
{
    internal class Program
    {
        static void Main(string[] args)
        {
            // Задание 1: Проверка на палиндром
            Console.WriteLine("=== Задание 1: Проверка на палиндром ===");
            Console.Write("Введите число: ");
            string input1 = Console.ReadLine();

            if (!string.IsNullOrEmpty(input1))
            {
                // Удаляем знак минуса для проверки
                string number1 = input1.StartsWith("-") ? input1.Substring(1) : input1;

                // Проверяем, что введены только цифры
                bool isValidNumber1 = true;
                foreach (char c in number1)
                {
                    if (!char.IsDigit(c))
                    {
                        isValidNumber1 = false;
                        break;
                    }
                }

                if (isValidNumber1)
                {
                    // Проверка на палиндром
                    bool isPalindrome = true;
                    int left = 0;
                    int right = number1.Length - 1;

                    while (left < right)
                    {
                        if (number1[left] != number1[right])
                        {
                            isPalindrome = false;
                            break;
                        }
                        left++;
                        right--;
                    }

                    Console.WriteLine($"Число {input1} {(isPalindrome ? "является" : "не является")} палиндромом.");
                }
                else
                {
                    Console.WriteLine("Ошибка: введите корректное число.");
                }
            }
            else
            {
                Console.WriteLine("Ошибка: пустой ввод.");
            }

            Console.WriteLine();

            // Задание 2: Циклический сдвиг
            Console.WriteLine("=== Задание 2: Циклический сдвиг ===");
            Console.Write("Введите число: ");
            string input2 = Console.ReadLine();

            if (!string.IsNullOrEmpty(input2))
            {
                // Проверяем знак
                bool isNegative = input2.StartsWith("-");
                string number2 = isNegative ? input2.Substring(1) : input2;

                // Проверяем, что введены только цифры
                bool isValidNumber2 = true;
                foreach (char c in number2)
                {
                    if (!char.IsDigit(c))
                    {
                        isValidNumber2 = false;
                        break;
                    }
                }

                if (isValidNumber2)
                {
                    Console.Write("Введите количество разрядов для сдвига: ");
                    if (int.TryParse(Console.ReadLine(), out int shiftCount))
                    {
                        Console.Write("Введите направление сдвига (L - влево, R - вправо): ");
                        string direction = Console.ReadLine()?.ToUpper();

                        if (direction == "L" || direction == "R")
                        {
                            // Выполняем циклический сдвиг
                            string result;
                            if (number2.Length <= 1)
                            {
                                result = number2;
                            }
                            else
                            {
                                // Нормализуем количество сдвигов
                                shiftCount = shiftCount % number2.Length;
                                if (shiftCount == 0)
                                {
                                    result = number2;
                                }
                                else
                                {
                                    if (direction == "L")
                                    {
                                        // Сдвиг влево
                                        result = number2.Substring(shiftCount) + number2.Substring(0, shiftCount);
                                    }
                                    else
                                    {
                                        // Сдвиг вправо
                                        int rightShiftPos = number2.Length - shiftCount;
                                        result = number2.Substring(rightShiftPos) + number2.Substring(0, rightShiftPos);
                                    }
                                }
                            }

                            string finalResult = isNegative ? "-" + result : result;
                            Console.WriteLine($"Результат: {finalResult}");
                        }
                        else
                        {
                            Console.WriteLine("Ошибка: введите L для сдвига влево или R для сдвига вправо.");
                        }
                    }
                    else
                    {
                        Console.WriteLine("Ошибка: введите корректное число разрядов.");
                    }
                }
                else
                {
                    Console.WriteLine("Ошибка: введите корректное число.");
                }
            }
            else
            {
                Console.WriteLine("Ошибка: пустой ввод.");
            }

            Console.WriteLine();

            // Задание 3: Поиск самой длинной неубывающей цепочки
            Console.WriteLine("=== Задание 3: Поиск самой длинной неубывающей цепочки ===");
            Console.WriteLine("Введите 15 целых чисел:");

            int[] numbers = new int[15];
            for (int i = 0; i < 15; i++)
            {
                Console.Write($"Число {i + 1}: ");
                while (!int.TryParse(Console.ReadLine(), out numbers[i]))
                {
                    Console.WriteLine("Ошибка: введите корректное целое число.");
                    Console.Write($"Число {i + 1}: ");
                }
            }

            int maxLength = 1;
            int maxStartIndex = 0;
            int currentLength = 1;
            int currentStartIndex = 0;

            for (int i = 1; i < 15; i++)
            {
                if (numbers[i] >= numbers[i - 1])
                {
                    currentLength++;
                }
                else
                {
                    if (currentLength > maxLength)
                    {
                        maxLength = currentLength;
                        maxStartIndex = currentStartIndex;
                    }
                    currentLength = 1;
                    currentStartIndex = i;
                }
            }

            // Проверяем последнюю цепочку
            if (currentLength > maxLength)
            {
                maxLength = currentLength;
                maxStartIndex = currentStartIndex;
            }

            Console.WriteLine($"Максимальная длина неубывающей цепочки: {maxLength}");
            Console.WriteLine($"Порядковый номер числа, с которого началась цепочка: {maxStartIndex + 1}");

            // Показываем саму цепочку для наглядности
            Console.Write("Цепочка: ");
            for (int i = maxStartIndex; i < maxStartIndex + maxLength; i++)
            {
                Console.Write(numbers[i]);
                if (i < maxStartIndex + maxLength - 1)
                    Console.Write(" -> ");
            }
            Console.WriteLine();
        }
    }
}